{"name": "@nocobase/plugin-users", "displayName": "Users", "displayName.zh-CN": "用户", "description": "Provides basic user model, as well as created by and updated by fields.", "description.zh-CN": "提供了基础的用户模型，以及创建人和最后更新人字段。", "version": "1.8.14", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/users", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/users", "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "jsonwebtoken": "^9.0.2"}, "peerDependencies": {"@nocobase/actions": "1.x", "@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-acl": "1.x", "@nocobase/plugin-auth": "1.x", "@nocobase/plugin-system-settings": "1.x", "@nocobase/plugin-ui-schema-storage": "1.x", "@nocobase/plugin-user-data-sync": "1.x", "@nocobase/resourcer": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Users & permissions"]}