{"name": "@nocobase/plugin-mobile", "version": "1.8.14", "main": "dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/mobile", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/mobile", "license": "AGPL-3.0", "displayName": "Mobile", "displayName.zh-CN": "移动端", "description": "Provides the ability to configure mobile pages.", "description.zh-CN": "提供移动端页面配置的能力。", "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/plugin-acl": "1.x", "@nocobase/plugin-localization": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "devDependencies": {"@ant-design/icons": "5.x", "@emotion/css": "11.x", "@formily/antd-v5": "1.x", "@formily/core": "2.x", "@formily/react": "2.x", "@formily/shared": "2.x", "@types/react": "18.x", "@types/react-dom": "18.x", "ahooks": "3.x", "antd": "5.x", "antd-mobile": "^5.38", "lodash": "4.x", "re-resizable": "6.6.0", "react-device-detect": "2.2.3", "react-i18next": "11.x"}}