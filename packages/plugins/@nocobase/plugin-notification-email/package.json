{"name": "@nocobase/plugin-notification-email", "version": "1.8.14", "displayName": "Notification: Email", "displayName.zh-CN": "通知：电子邮件", "description": "Used for sending email notifications with built-in SMTP transport.", "description.zh-CN": "通过电子邮件渠道发送通知，目前只支持 SMTP 传输方式。", "homepage": "https://docs.nocobase.com/handbook/notification-email", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/notification-email", "main": "dist/server/index.js", "devDependencies": {"@types/nodemailer": "^6.x", "nodemailer": "^6.x"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/plugin-notification-manager": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "keywords": ["Notification"]}