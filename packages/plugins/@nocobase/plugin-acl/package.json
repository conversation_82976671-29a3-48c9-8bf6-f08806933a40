{"name": "@nocobase/plugin-acl", "displayName": "Access control", "displayName.zh-CN": "权限控制", "description": "Based on roles, resources, and actions, access control can precisely manage interface configuration permissions, data operation permissions, menu access permissions, and plugin permissions.", "description.zh-CN": "基于角色、资源和操作的权限控制，可以精确控制界面配置权限、数据操作权限、菜单访问权限、插件权限。", "version": "1.8.14", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/acl", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/acl", "keywords": ["Users & permissions"], "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "jsonwebtoken": "^9.0.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"@nocobase/acl": "1.x", "@nocobase/actions": "1.x", "@nocobase/cache": "1.x", "@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/plugins/acl"}}