{"name": "@nocobase/plugin-backup-restore", "displayName": "App backup & restore (deprecated)", "displayName.zh-CN": "应用的备份与还原（废弃）", "description": "Backup and restore applications for scenarios such as application replication, migration, and upgrades.", "description.zh-CN": "备份和还原应用，可用于应用的复制、迁移、升级等场景。", "version": "1.8.14", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/backup-restore", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/backup-restore", "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/plugins/plugin-backup-restore"}, "devDependencies": {"@koa/multer": "^3.0.2", "@types/archiver": "^5.3.1", "antd": "5.x", "archiver": "^5.3.1", "content-disposition": "^0.5.4", "dayjs": "^1.11.8", "decompress": "^4.2.1", "inquirer": "^8.0.0", "koa-send": "^5.0.1", "mkdirp": "^1.0.4", "object-path": "^0.11.8", "react": "^18.2.0", "semver": "^7.7.1", "tar": "^7.4.3"}, "peerDependencies": {"@nocobase/actions": "1.x", "@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "keywords": ["System management"], "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}