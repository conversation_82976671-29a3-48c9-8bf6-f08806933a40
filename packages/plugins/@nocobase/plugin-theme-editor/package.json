{"name": "@nocobase/plugin-theme-editor", "version": "1.8.14", "main": "dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/theme-editor", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/theme-editor", "displayName": "Theme editor", "displayName.zh-CN": "主题编辑器", "description": "Customize UI colors, sizes, etc. and save the result as a theme to switch between multiple themes.", "description.zh-CN": "自定义 UI 的颜色、尺寸等，并将结果保存为主题，可在多个主题间切换。", "devDependencies": {"@ant-design/cssinjs": "^1.11.1", "@ant-design/icons": "5.x", "@arvinxu/layout-kit": "^1", "@ctrl/tinycolor": "^3.6.0", "@emotion/css": "^11.11.2", "antd": "5.x", "classnames": "^2.3.1", "lodash": "4.17.21", "rc-util": "^5.32.0", "react": "^18.2.0", "react-colorful": "^5.5.1", "tinycolor2": "^1.6.0", "use-debouncy": "^4.3.0", "vanilla-jsoneditor": "^0.17.8"}, "peerDependencies": {"@nocobase/actions": "1.x", "@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["System management"]}