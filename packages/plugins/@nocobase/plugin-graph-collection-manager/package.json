{"name": "@nocobase/plugin-graph-collection-manager", "displayName": "Graph collection manager", "displayName.zh-CN": "可视化数据表管理", "description": "An ER diagram-like tool. Currently only the Master database is supported.", "description.zh-CN": "类似 ER 图的工具，目前只支持主数据库。", "version": "1.8.14", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/graph-collection-manager", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/graph-collection-manager", "devDependencies": {"@ant-design/icons": "5.x", "@antv/x6": "^2.0.0", "@antv/x6-plugin-dnd": "^2.0.0", "@antv/x6-plugin-export": "^2.0.0", "@antv/x6-plugin-minimap": "^2.0.0", "@antv/x6-plugin-scroller": "^2.0.0", "@antv/x6-plugin-selection": "^2.0.0", "@antv/x6-plugin-snapline": "^2.0.0", "@antv/x6-react-shape": "^2.0.0", "@formily/react": "2.x", "@formily/reactive": "2.x", "@formily/shared": "2.x", "ahooks": "^3.7.2", "antd": "5.x", "dagre": "^0.8.5", "react": "^18.2.0", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Data model tools"]}