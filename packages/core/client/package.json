{"name": "@nocobase/client", "version": "1.8.14", "license": "AGPL-3.0", "main": "lib/index.js", "module": "es/index.mjs", "types": "es/index.d.ts", "dependencies": {"@ahooksjs/use-url-state": "3.5.1", "@ant-design/cssinjs": "^1.11.1", "@ant-design/icons": "^5.6.1", "@ant-design/pro-layout": "^7.22.1", "@antv/g2plot": "^2.4.18", "@budibase/handlebars-helpers": "0.14.0", "@ctrl/tinycolor": "^3.6.0", "@dnd-kit/core": "^6.0.0", "@dnd-kit/sortable": "^7.0.0", "@emotion/css": "^11.7.1", "@formily/antd-v5": "1.2.3", "@formily/core": "^2.2.27", "@formily/grid": "^2.2.27", "@formily/json-schema": "^2.2.27", "@formily/path": "^2.2.27", "@formily/react": "^2.2.27", "@formily/reactive": "^2.2.27", "@formily/reactive-react": "^2.2.27", "@formily/shared": "^2.2.27", "@formily/validator": "^2.2.27", "@nocobase/evaluators": "1.8.14", "@nocobase/sdk": "1.8.14", "@nocobase/utils": "1.8.14", "ahooks": "^3.7.2", "antd": "5.24.2", "antd-style": "3.7.1", "axios": "^1.7.0", "bignumber.js": "^9.1.2", "classnames": "^2.3.1", "cronstrue": "^2.11.0", "file-saver": "^2.0.5", "filesize": "9.0.11", "flat": "^5.0.2", "html5-qrcode": "^2.3.8", "i18next": "^22.4.9", "i18next-http-backend": "^2.1.1", "ignore": "^5.2.0", "json5": "^2.2.3", "lodash": "4.17.21", "lru-cache": "6.0.0", "markdown-it": "14.1.0", "markdown-it-highlightjs": "3.3.1", "mathjs": "^10.6.0", "mermaid": "9.4.3", "mime": "^4.0.4", "mime-match": "^1.0.2", "react-beautiful-dnd": "^13.1.0", "react-device-detect": "2.2.3", "react-drag-listview": "^0.1.9", "react-error-boundary": "^4.0.10", "react-helmet": "^6.1.0", "react-hotkeys-hook": "^3.4.7", "react-i18next": "^11.15.1", "react-iframe": "~1.8.5", "react-image-lightbox": "^5.1.4", "react-intersection-observer": "9.14.0", "react-js-cron": "^3.1.0", "react-quill": "^2.0.0", "react-router-dom": "^6.11.2", "react-to-print": "^2.14.7", "sanitize-html": "2.13.0", "use-deep-compare-effect": "^1.8.1"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "react-is": ">=18.0.0"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@types/markdown-it": "14.1.1", "@types/markdown-it-highlightjs": "3.3.1", "@types/react-big-calendar": "^1.6.4", "axios-mock-adapter": "^1.20.0", "dumi": "2.2.14", "dumi-theme-nocobase": "^0.2.28"}}