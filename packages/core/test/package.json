{"name": "@nocobase/test", "version": "1.8.14", "main": "lib/index.js", "module": "./src/index.ts", "types": "./lib/index.d.ts", "license": "AGPL-3.0", "exports": {".": {"require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "import": {"types": "./es/index.d.ts", "default": "./es/index.mjs"}}, "./client": {"require": {"types": "./lib/client/index.d.ts", "default": "./lib/client/index.js"}, "import": {"types": "./es/client/index.d.ts", "default": "./es/client/index.mjs"}}, "./web": {"require": {"types": "./lib/web/index.d.ts", "default": "./lib/web/index.js"}, "import": {"types": "./es/web/index.d.ts", "default": "./es/web/index.mjs"}}, "./e2e": {"require": {"types": "./lib/e2e/index.d.ts", "default": "./lib/e2e/index.js"}, "import": {"types": "./es/e2e/index.d.ts", "default": "./es/e2e/index.mjs"}}, "./package.json": "./package.json", "./vitest.mjs": "./vitest.mjs"}, "dependencies": {"@faker-js/faker": "8.1.0", "@nocobase/server": "1.8.14", "@playwright/test": "^1.45.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.0.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/supertest": "^2.0.11", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-istanbul": "^1.5.0", "@vitest/coverage-v8": "^1.5.0", "axios-mock-adapter": "1.22.0", "jsdom": "^25.0.1", "jsdom-worker": "^0.3.0", "mariadb": "^2.5.6", "mockjs": "^1.1.0", "mysql2": "^3.11.0", "pg": "^8.7.3", "pg-hstore": "^2.3.4", "supertest": "^6.1.6", "vite": "^5.0.0", "vitest": "^1.5.0", "vitest-dom": "^0.1.1", "ws": "^8.13.0"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}