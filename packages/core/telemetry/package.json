{"name": "@nocobase/telemetry", "version": "1.8.14", "description": "nocobase telemetry library", "license": "AGPL-3.0", "main": "./lib/index.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/telemetry"}, "dependencies": {"@nocobase/utils": "1.8.14", "@opentelemetry/api": "^1.7.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/resources": "^1.19.0", "@opentelemetry/sdk-metrics": "^1.19.0", "@opentelemetry/sdk-trace-base": "^1.19.0", "@opentelemetry/sdk-trace-node": "^1.19.0", "@opentelemetry/semantic-conventions": "^1.19.0"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}