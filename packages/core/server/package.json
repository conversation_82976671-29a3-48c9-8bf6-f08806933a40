{"name": "@nocobase/server", "version": "1.8.14", "main": "lib/index.js", "types": "./lib/index.d.ts", "license": "AGPL-3.0", "dependencies": {"@formily/json-schema": "2.x", "@hapi/topo": "^6.0.0", "@koa/cors": "^5.0.0", "@koa/multer": "^3.1.0", "@koa/router": "^13.1.0", "@nocobase/acl": "1.8.14", "@nocobase/actions": "1.8.14", "@nocobase/auth": "1.8.14", "@nocobase/cache": "1.8.14", "@nocobase/data-source-manager": "1.8.14", "@nocobase/database": "1.8.14", "@nocobase/evaluators": "1.8.14", "@nocobase/lock-manager": "1.8.14", "@nocobase/logger": "1.8.14", "@nocobase/resourcer": "1.8.14", "@nocobase/sdk": "1.8.14", "@nocobase/telemetry": "1.8.14", "@nocobase/utils": "1.8.14", "@types/decompress": "4.2.7", "@types/ini": "^1.3.31", "@types/koa-send": "^4.1.3", "@types/multer": "^1.4.12", "async-mutex": "^0.5.0", "axios": "^1.7.0", "chalk": "^4.1.1", "commander": "^9.2.0", "compression": "^1.8.0", "cron": "^2.4.4", "cronstrue": "^2.11.0", "dayjs": "^1.11.8", "decompress": "4.2.1", "find-package-json": "^1.2.0", "fs-extra": "^11.1.1", "i18next": "^22.4.9", "ini": "^4.1.1", "koa": "^2.15.4", "koa-bodyparser": "^4.3.0", "koa-send": "^5.0.1", "koa-static": "^5.0.0", "lodash": "^4.17.21", "multer": "^1.4.5-lts.2", "nanoid": "^3.3.11", "semver": "^7.7.1", "serve-handler": "^6.1.6", "ws": "^8.13.0", "xpipe": "^1.0.5"}, "devDependencies": {"@types/semver": "^7.3.9", "@types/serve-handler": "^6.1.1", "@types/ws": "^8.5.5"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}