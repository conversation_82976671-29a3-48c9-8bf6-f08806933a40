{"name": "@nocobase/devtools", "version": "1.8.14", "description": "", "license": "AGPL-3.0", "main": "./src/index.js", "dependencies": {"@nocobase/build": "1.8.14", "@nocobase/client": "1.8.14", "@nocobase/test": "1.8.14", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.4", "@types/lodash": "^4.14.177", "@types/node": "*", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "concurrently": "^7.0.0", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^7.1.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "fast-glob": "^3.3.1", "lerna": "^4.0.0", "prettier": "^3.0.0", "pretty-format": "^24.0.0", "pretty-quick": "^3.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "rimraf": "^3.0.0", "serve": "^14.2.4", "tinybench": "^4.0.1", "ts-loader": "^7.0.4", "ts-node": "9.1.1", "ts-node-dev": "1.1.8", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.2", "typescript": "5.1.3", "umi": "^4.0.69"}, "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/core/devtools"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}