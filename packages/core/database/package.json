{"name": "@nocobase/database", "version": "1.8.14", "description": "", "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "AGPL-3.0", "dependencies": {"@nocobase/logger": "1.8.14", "@nocobase/utils": "1.8.14", "async-mutex": "^0.3.2", "chalk": "^4.1.1", "cron-parser": "4.4.0", "dayjs": "^1.11.8", "deepmerge": "^4.2.2", "excel-date-to-js": "^1.1.5", "exponential-backoff": "^3.1.1", "flat": "^5.0.2", "glob": "^7.1.6", "graphlib": "^2.1.8", "lodash": "^4.17.21", "mathjs": "^10.6.1", "nanoid": "^3.3.11", "node-fetch": "^2.6.7", "node-sql-parser": "^4.18.0", "qs": "^6.11.2", "safe-json-stringify": "^1.2.0", "semver": "^7.7.1", "sequelize": "^6.26.0", "umzug": "^3.1.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/glob": "^7.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/database"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}